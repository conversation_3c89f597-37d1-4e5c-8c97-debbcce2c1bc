import {
  doc,
  updateDoc,
  onSnapshot,
  arrayUnion,
  arrayRemove,
  Timestamp,
  getDoc,
  setDoc,
  collection,
  getDocs
} from 'firebase/firestore';
import { db } from '../lib/firebase';
import { COLLECTIONS } from '../types/firebase';
import type { Book, BookCollaborator, UserRole, EditChange, Scene, Chapter, Character, PlotPoint } from '../types';

/**
 * Service for managing collaborative editing features
 */
export class CollaborativeEditingService {
  
  /**
   * Add a collaborator to a book
   */
  static async addCollaborator(
    bookId: string,
    collaboratorEmail: string,
    role: UserRole,
    addedByUserId: string,
    addedByUserName: string
  ): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      // First, find the user by email (using our user lookup service)
      const { UserLookupService } = await import('./userLookupService');
      const userValidation = await UserLookupService.validateRecipientEmail(collaboratorEmail);
      
      if (!userValidation.isValid || !userValidation.user) {
        return {
          success: false,
          error: userValidation.error || 'User not found'
        };
      }

      const collaborator: BookCollaborator = {
        userId: userValidation.user.uid,
        userEmail: collaboratorEmail.toLowerCase(),
        userName: userValidation.user.displayName || collaboratorEmail.split('@')[0],
        role,
        addedAt: new Date().toISOString(),
        addedBy: addedByUserId,
        isActive: true
      };

      const bookRef = doc(db, COLLECTIONS.BOOKS, bookId);
      await updateDoc(bookRef, {
        collaborators: arrayUnion(collaborator),
        isCollaborative: true,
        updatedAt: new Date().toISOString()
      });

      // Create notification for the new collaborator
      try {
        const { NotificationService } = await import('./notificationService');
        await NotificationService.createNotification(
          userValidation.user.uid,
          'share_received',
          'Added as Collaborator',
          `${addedByUserName} added you as ${role} to a book`,
          {
            bookId,
            senderName: addedByUserName
          }
        );
      } catch (notificationError) {
        console.error('Failed to create collaboration notification:', notificationError);
      }

      return { success: true };
    } catch (error) {
      console.error('Error adding collaborator:', error);
      return {
        success: false,
        error: 'Failed to add collaborator'
      };
    }
  }

  /**
   * Remove a collaborator from a book
   */
  static async removeCollaborator(
    bookId: string,
    collaboratorUserId: string
  ): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      // Get current book data to find the collaborator
      const bookRef = doc(db, COLLECTIONS.BOOKS, bookId);
      const bookSnap = await getDoc(bookRef);
      
      if (!bookSnap.exists()) {
        return {
          success: false,
          error: 'Book not found'
        };
      }

      const bookData = bookSnap.data() as Book;
      const collaboratorToRemove = bookData.collaborators?.find(
        c => c.userId === collaboratorUserId
      );

      if (!collaboratorToRemove) {
        return {
          success: false,
          error: 'Collaborator not found'
        };
      }

      await updateDoc(bookRef, {
        collaborators: arrayRemove(collaboratorToRemove),
        updatedAt: new Date().toISOString()
      });

      return { success: true };
    } catch (error) {
      console.error('Error removing collaborator:', error);
      return {
        success: false,
        error: 'Failed to remove collaborator'
      };
    }
  }

  /**
   * Update collaborator role
   */
  static async updateCollaboratorRole(
    bookId: string,
    collaboratorUserId: string,
    newRole: UserRole
  ): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      const bookRef = doc(db, COLLECTIONS.BOOKS, bookId);
      const bookSnap = await getDoc(bookRef);
      
      if (!bookSnap.exists()) {
        return {
          success: false,
          error: 'Book not found'
        };
      }

      const bookData = bookSnap.data() as Book;
      const updatedCollaborators = bookData.collaborators?.map(collaborator => 
        collaborator.userId === collaboratorUserId 
          ? { ...collaborator, role: newRole }
          : collaborator
      ) || [];

      await updateDoc(bookRef, {
        collaborators: updatedCollaborators,
        updatedAt: new Date().toISOString()
      });

      return { success: true };
    } catch (error) {
      console.error('Error updating collaborator role:', error);
      return {
        success: false,
        error: 'Failed to update collaborator role'
      };
    }
  }

  /**
   * Check if user has access to a book and their role
   */
  static getUserRoleInBook(book: Book, userId: string): {
    hasAccess: boolean;
    role: UserRole | null;
    isOwner: boolean;
  } {
    // Check if user is the owner
    if (book.ownerId === userId) {
      return {
        hasAccess: true,
        role: 'author',
        isOwner: true
      };
    }

    // Check if user is a collaborator
    const collaborator = book.collaborators?.find(c => c.userId === userId && c.isActive);
    
    if (collaborator) {
      return {
        hasAccess: true,
        role: collaborator.role,
        isOwner: false
      };
    }

    return {
      hasAccess: false,
      role: null,
      isOwner: false
    };
  }

  /**
   * Track an edit change made by an editor
   */
  static async trackEditChange(
    bookId: string,
    sceneId: string,
    change: Omit<EditChange, 'id' | 'timestamp'>
  ): Promise<{
    success: boolean;
    changeId?: string;
    error?: string;
  }> {
    try {
      const changeId = `change_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      const editChange: EditChange = {
        ...change,
        id: changeId,
        timestamp: new Date().toISOString()
      };

      // Get the book document to find the scene and update it
      const bookRef = doc(db, COLLECTIONS.BOOKS, bookId);
      const bookSnap = await getDoc(bookRef);
      
      if (!bookSnap.exists()) {
        throw new Error('Book not found');
      }

      const bookData = bookSnap.data() as Book;
      
      // Find the scene and add the edit change
      const updatedChapters = bookData.chapters.map(chapter => ({
        ...chapter,
        scenes: chapter.scenes.map(scene => {
          if (scene.id === sceneId) {
            return {
              ...scene,
              editChanges: [...(scene.editChanges || []), editChange],
              lastEditedBy: change.userId,
              lastEditedAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            };
          }
          return scene;
        })
      }));

      await updateDoc(bookRef, {
        chapters: updatedChapters,
        updatedAt: new Date().toISOString()
      });

      return {
        success: true,
        changeId
      };
    } catch (error) {
      console.error('Error tracking edit change:', error);
      return {
        success: false,
        error: 'Failed to track edit change'
      };
    }
  }

  /**
   * Accept or reject an edit change
   */
  static async reviewEditChange(
    bookId: string,
    sceneId: string,
    changeId: string,
    isAccepted: boolean,
    reviewedByUserId: string
  ): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      // Get the book document to find the scene and update the change
      const bookRef = doc(db, COLLECTIONS.BOOKS, bookId);
      const bookSnap = await getDoc(bookRef);
      
      if (!bookSnap.exists()) {
        return {
          success: false,
          error: 'Book not found'
        };
      }

      const bookData = bookSnap.data() as Book;
      
      // Find the scene and update the specific change
      const updatedChapters = bookData.chapters.map(chapter => ({
        ...chapter,
        scenes: chapter.scenes.map(scene => {
          if (scene.id === sceneId) {
            const updatedChanges = scene.editChanges?.map(change => 
              change.id === changeId 
                ? { 
                    ...change, 
                    isAccepted, 
                    acceptedBy: reviewedByUserId,
                    acceptedAt: new Date().toISOString()
                  }
                : change
            ) || [];

            return {
              ...scene,
              editChanges: updatedChanges,
              updatedAt: new Date().toISOString()
            };
          }
          return scene;
        })
      }));

      await updateDoc(bookRef, {
        chapters: updatedChapters,
        updatedAt: new Date().toISOString()
      });

      return { success: true };
    } catch (error) {
      console.error('Error reviewing edit change:', error);
      return {
        success: false,
        error: 'Failed to review edit change'
      };
    }
  }

  /**
   * Get all books where user is a collaborator (lightweight version)
   */
  static async getUserCollaborativeBooks(userId: string): Promise<Book[]> {
    try {
      console.log('🔍 Getting collaborative books for user:', userId);

      // More efficient approach: get only books where user is a collaborator
      // Note: This requires a composite index on collaborators.userId and collaborators.isActive
      // For now, we'll use the simpler approach but optimize it

      const booksQuery = collection(db, COLLECTIONS.BOOKS);
      const booksSnapshot = await getDocs(booksQuery);

      console.log(`📚 Scanning ${booksSnapshot.docs.length} books for collaborations`);

      const collaborativeBooks: Book[] = [];

      for (const bookDoc of booksSnapshot.docs) {
        const bookData = bookDoc.data();

        // Quick check: does this book have collaborators and is user one of them?
        const collaborators = bookData.collaborators || [];
        const isCollaborator = collaborators.some(
          (collaborator: any) => collaborator.userId === userId && collaborator.isActive
        );

        if (isCollaborator && bookData.ownerId !== userId) {
          console.log(`✅ Found collaborative book: ${bookData.title}`);

          // Return lightweight book data - load full data only when needed
          const lightweightBook: Book = {
            id: bookDoc.id,
            title: bookData.title || '',
            author: bookData.author || '',
            projectType: bookData.projectType || 'novel',
            ownerId: bookData.ownerId || '',
            createdAt: bookData.createdAt || new Date().toISOString(),
            updatedAt: bookData.updatedAt || new Date().toISOString(),
            wordCount: bookData.wordCount || 0,
            chapters: [], // Load on demand
            characters: [], // Load on demand
            plotPoints: [], // Load on demand
            collaborators: collaborators,
            isCollaborative: bookData.isCollaborative || false,
            worldbuilding: bookData.worldbuilding || {
              locations: [], institutions: [], politics: [], hierarchies: [],
              cultures: [], religions: [], languages: [], technologies: [],
              economies: [], conflicts: []
            },
            settings: bookData.settings || {
              fontSize: 16, fontFamily: 'serif', theme: 'light',
              lineHeight: 1.6, paragraphSpacing: 1.2
            }
          };

          collaborativeBooks.push(lightweightBook);
        }
      }

      console.log(`🎉 Found ${collaborativeBooks.length} collaborative books for user`);
      return collaborativeBooks;
    } catch (error) {
      console.error('❌ Error getting collaborative books:', error);
      return [];
    }
  }

  /**
   * Load complete book data on demand (when book is actually opened)
   */
  static async loadCompleteBookData(bookId: string): Promise<Book | null> {
    try {
      console.log(`📚 Loading complete data for book: ${bookId}`);

      // Get the book document first
      const bookRef = doc(db, COLLECTIONS.BOOKS, bookId);
      const bookSnap = await getDoc(bookRef);

      if (!bookSnap.exists()) {
        console.error('Book not found:', bookId);
        return null;
      }

      const bookData = bookSnap.data();

      // Load chapters and scenes in parallel for better performance
      const [chaptersSnapshot, charactersSnapshot, plotPointsSnapshot] = await Promise.all([
        getDocs(collection(db, COLLECTIONS.BOOKS, bookId, 'chapters')),
        getDocs(collection(db, COLLECTIONS.BOOKS, bookId, 'characters')),
        getDocs(collection(db, COLLECTIONS.BOOKS, bookId, 'plotPoints'))
      ]);

      // Load chapters with scenes
      const chapters = [];
      for (const chapterDoc of chaptersSnapshot.docs) {
        const chapterData = chapterDoc.data();

        // Load scenes for this chapter
        const scenesSnapshot = await getDocs(
          collection(db, COLLECTIONS.BOOKS, bookId, 'chapters', chapterDoc.id, 'scenes')
        );

        const scenes: Scene[] = scenesSnapshot.docs.map(sceneDoc => ({
          id: sceneDoc.id,
          chapterId: chapterDoc.id,
          title: sceneDoc.data().title || '',
          content: sceneDoc.data().content || '',
          wordCount: sceneDoc.data().wordCount || 0,
          order: sceneDoc.data().order || 0,
          orderIndex: sceneDoc.data().orderIndex || 0,
          createdAt: sceneDoc.data().createdAt || new Date().toISOString(),
          updatedAt: sceneDoc.data().updatedAt || new Date().toISOString(),
          editChanges: sceneDoc.data().editChanges || [],
          lastEditedBy: sceneDoc.data().lastEditedBy,
          lastEditedAt: sceneDoc.data().lastEditedAt
        }));

        scenes.sort((a, b) => (a.order || 0) - (b.order || 0));

        const chapter: Chapter = {
          id: chapterDoc.id,
          bookId: bookId,
          title: chapterData.title || '',
          content: chapterData.content || '',
          wordCount: chapterData.wordCount || 0,
          scenes: scenes,
          order: chapterData.order || 0,
          orderIndex: chapterData.orderIndex || 0,
          createdAt: chapterData.createdAt || new Date().toISOString(),
          updatedAt: chapterData.updatedAt || new Date().toISOString()
        };

        chapters.push(chapter);
      }

      chapters.sort((a, b) => (a.order || 0) - (b.order || 0));

      // Process characters and plot points
      const characters: Character[] = charactersSnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          bookId: bookId,
          name: data.name || '',
          description: data.description || null,
          role: data.role || null,
          notes: data.notes || null,
          appearance: data.appearance || null,
          personality: data.personality || null,
          backstory: data.backstory || null,
          goals: data.goals || null,
          createdAt: data.createdAt || new Date().toISOString(),
          updatedAt: data.updatedAt || new Date().toISOString()
        };
      });

      const plotPoints: PlotPoint[] = plotPointsSnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          bookId: bookId,
          title: data.title || '',
          description: data.description || '',
          type: data.type || 'plot',
          order: data.order || 0,
          orderIndex: data.orderIndex || 0,
          createdAt: data.createdAt || new Date().toISOString(),
          updatedAt: data.updatedAt || new Date().toISOString()
        };
      });

      // Calculate total word count
      const totalWordCount = chapters.reduce((total, chapter) => {
        return total + (chapter.scenes || []).reduce((chapterTotal, scene) => {
          return chapterTotal + (scene.wordCount || 0);
        }, 0);
      }, 0);

      console.log(`📊 Loaded ${chapters.length} chapters, ${characters.length} characters, ${plotPoints.length} plot points`);
      console.log(`📝 Total word count: ${totalWordCount}`);

      return {
        id: bookId,
        ...bookData,
        chapters: chapters,
        characters: characters,
        plotPoints: plotPoints,
        wordCount: totalWordCount,
        collaborators: bookData.collaborators || [],
        isCollaborative: bookData.isCollaborative || false,
        worldbuilding: bookData.worldbuilding || {
          locations: [], institutions: [], politics: [], hierarchies: [],
          cultures: [], religions: [], languages: [], technologies: [],
          economies: [], conflicts: []
        },
        settings: bookData.settings || {
          fontSize: 16, fontFamily: 'serif', theme: 'light',
          lineHeight: 1.6, paragraphSpacing: 1.2
        }
      };
    } catch (error) {
      console.error('❌ Error loading complete book data:', error);
      return null;
    }
  }



  /**
   * Subscribe to real-time book updates for collaboration
   */
  static subscribeToBookUpdates(
    bookId: string,
    onBookUpdate: (book: Book) => void,
    onError?: (error: Error) => void
  ) {
    const bookRef = doc(db, COLLECTIONS.BOOKS, bookId);
    
    return onSnapshot(
      bookRef,
      (docSnapshot) => {
        if (docSnapshot.exists()) {
          const bookData = docSnapshot.data() as Book;
          onBookUpdate({ ...bookData, id: docSnapshot.id });
        }
      },
      (error) => {
        console.error('Error listening to book updates:', error);
        if (onError) onError(error);
      }
    );
  }
}
