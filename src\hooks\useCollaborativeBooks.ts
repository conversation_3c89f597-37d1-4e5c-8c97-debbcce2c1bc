import { useState, useEffect, useCallback } from 'react';
import { CollaborativeEditingService } from '../services/collaborativeEditingService';
import type { Book } from '../types';

/**
 * Hook for managing books where the user is a collaborator
 */
export function useCollaborativeBooks(userId: string) {
  const [collaborativeBooks, setCollaborativeBooks] = useState<Book[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastFetchTime, setLastFetchTime] = useState<number>(0);

  /**
   * Fetch collaborative books with caching
   */
  const fetchCollaborativeBooks = useCallback(async (force = false) => {
    if (!userId) {
      console.log('🔍 No userId provided, clearing collaborative books');
      setCollaborativeBooks([]);
      return;
    }

    // Prevent excessive fetching - cache for 30 seconds
    const now = Date.now();
    const cacheTime = 30 * 1000; // 30 seconds

    if (!force && (now - lastFetchTime) < cacheTime) {
      console.log('🔍 Using cached collaborative books (fetched recently)');
      return;
    }

    console.log('🔍 Fetching collaborative books for userId:', userId);
    setLoading(true);
    setError(null);

    try {
      const books = await CollaborativeEditingService.getUserCollaborativeBooks(userId);
      console.log('🔍 Hook received collaborative books:', books.length);
      setCollaborativeBooks(books);
      setLastFetchTime(now);
    } catch (err) {
      console.error('❌ Error fetching collaborative books:', err);
      setError('Failed to load collaborative books');
    } finally {
      setLoading(false);
    }
  }, [userId, lastFetchTime]);

  /**
   * Refresh collaborative books (force refresh)
   */
  const refreshCollaborativeBooks = useCallback(() => {
    fetchCollaborativeBooks(true); // Force refresh
  }, [fetchCollaborativeBooks]);

  /**
   * Get user's role in a specific book
   */
  const getUserRoleInBook = useCallback((bookId: string) => {
    const book = collaborativeBooks.find(b => b.id === bookId);
    if (!book) return null;

    return CollaborativeEditingService.getUserRoleInBook(book, userId);
  }, [collaborativeBooks, userId]);

  /**
   * Check if user has access to a book
   */
  const hasAccessToBook = useCallback((bookId: string) => {
    const roleInfo = getUserRoleInBook(bookId);
    return roleInfo?.hasAccess || false;
  }, [getUserRoleInBook]);

  /**
   * Get book by ID from collaborative books
   */
  const getCollaborativeBook = useCallback((bookId: string) => {
    return collaborativeBooks.find(book => book.id === bookId) || null;
  }, [collaborativeBooks]);

  // Fetch books when userId changes
  useEffect(() => {
    fetchCollaborativeBooks();
  }, [fetchCollaborativeBooks]);

  /**
   * Get a specific collaborative book with full data
   */
  const getCollaborativeBookWithFullData = useCallback(async (bookId: string) => {
    const book = collaborativeBooks.find(b => b.id === bookId);
    if (book) {
      // If we already have the book, refresh it to get latest data
      console.log('🔄 Refreshing collaborative book data for:', book.title);
      await refreshCollaborativeBooks();
      return collaborativeBooks.find(b => b.id === bookId);
    }
    return null;
  }, [collaborativeBooks, refreshCollaborativeBooks]);

  return {
    collaborativeBooks,
    loading,
    error,
    refreshCollaborativeBooks,
    getUserRoleInBook,
    hasAccessToBook,
    getCollaborativeBook,
    getCollaborativeBookWithFullData,
    fetchCollaborativeBooks
  };
}
