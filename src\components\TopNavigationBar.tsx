import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react';
import { NotificationBell } from './NotificationPanel';

interface TopNavigationBarProps {
  bookTitle: string;
  currentLocation: string;
  userId: string;
  onSettingsClick: () => void;
  onNotificationsClick: () => void;
  backgroundImage: string;
  className?: string;
}

export const TopNavigationBar: React.FC<TopNavigationBarProps> = ({
  bookTitle,
  currentLocation,
  userId,
  onSettingsClick,
  onNotificationsClick,
  backgroundImage,
  className = ''
}) => {
  const glassClassLight = 'bg-black/40';

  return (
    <div className={`${glassClassLight} backdrop-blur-md rounded-xl border border-white/20 p-3 mb-4 ${className}`}>
      <div className="flex items-center justify-between">
        {/* Left side - Book title and location */}
        <div className="flex items-center space-x-4">
          <h1 className="text-lg font-serif font-regular text-white">{bookTitle}</h1>
          <div className="flex items-center space-x-2 text-white/80 font-sans text-sm">
            <span>{currentLocation}</span>
          </div>
        </div>

        {/* Right side - Actions */}
        <div className="flex items-center space-x-2">
          {/* Notifications */}
          <NotificationBell
            userId={userId}
            onClick={onNotificationsClick}
            className="p-2 text-white/60 hover:text-white hover:bg-white/10 rounded-lg transition-colors"
          />

          {/* Settings */}
          <button
            onClick={onSettingsClick}
            className="p-2 text-white/60 hover:text-white hover:bg-white/10 rounded-lg transition-colors"
            title="Settings"
          >
            <Settings className="w-5 h-5" />
          </button>
        </div>
      </div>
    </div>
  );
};
