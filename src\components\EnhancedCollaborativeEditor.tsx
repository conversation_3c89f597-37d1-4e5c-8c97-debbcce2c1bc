import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import TextAlign from '@tiptap/extension-text-align';
import HorizontalRule from '@tiptap/extension-horizontal-rule';
import History from '@tiptap/extension-history';
import { Extension } from '@tiptap/core';
import { Plugin, PluginKey } from 'prosemirror-state';
import { Decoration, DecorationSet } from 'prosemirror-view';
import {
  Bold,
  Italic,
  Underline as UnderlineIcon,
  Strikethrough,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Minus,
  Undo,
  Redo,
  Check,
  X,
  User,
  Clock
} from 'lucide-react';
import type { UserRole, EditChange } from '../types';
import { CollaborativeEditingService } from '../services/collaborativeEditingService';

interface EnhancedCollaborativeEditorProps {
  content: string;
  onChange: (content: string) => void;
  bookId: string;
  sceneId: string;
  currentUserId: string;
  currentUserName: string;
  userRole: UserRole;
  editChanges?: EditChange[];
  onAcceptChange?: (changeId: string) => void;
  onRejectChange?: (changeId: string) => void;
  className?: string;
  placeholder?: string;
  hideToolbar?: boolean;
  readOnly?: boolean;
}

// Custom extension for tracking changes
const ChangeTrackingExtension = Extension.create({
  name: 'changeTracking',

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey('changeTracking'),
        state: {
          init() {
            return DecorationSet.empty;
          },
          apply(tr, decorationSet) {
            // Map existing decorations through the transaction
            decorationSet = decorationSet.map(tr.mapping, tr.doc);

            // Get edit changes from meta
            const editChanges = tr.getMeta('editChanges') || [];
            const pendingChanges = editChanges.filter((change: EditChange) => change.isAccepted === null);



            // Clear existing decorations and rebuild them
            decorationSet = DecorationSet.empty;

            const decorations: any[] = [];

            pendingChanges.forEach((change: EditChange) => {
              try {
                // Calculate positions more carefully
                let from = Math.max(0, change.position);
                let to = from;

                // Ensure positions are within document bounds
                const docSize = tr.doc.content.size;

                if (from > docSize) {
                  from = Math.max(0, docSize - 1);
                }

                if (change.type === 'insert' || change.type === 'replace') {
                  to = Math.min(docSize, from + Math.max(1, change.newText.length));
                } else if (change.type === 'delete') {
                  // For delete operations, highlight the position where text was deleted
                  to = Math.min(docSize, from + 1);
                } else {
                  to = Math.min(docSize, from + 1); // Default to single character
                }



                // Only create decoration if positions are valid
                if (from >= 0 && to <= docSize && from <= to) {
                  // Ensure we have at least a 1-character span for visibility
                  if (from === to && to < docSize) {
                    to = from + 1;
                  }

                  const decoration = Decoration.inline(from, to, {
                    class: `edit-change edit-${change.type}`,
                    'data-change-id': change.id,
                    'data-user': change.userName,
                    style: `background-color: ${change.type === 'insert' ? '#dbeafe' : change.type === 'delete' ? '#fee2e2' : '#fef3c7'} !important; border-bottom: 2px solid ${change.type === 'insert' ? '#3b82f6' : change.type === 'delete' ? '#ef4444' : '#f59e0b'} !important; padding: 2px 4px !important; margin: 0 2px !important; border-radius: 3px !important; display: inline !important;`
                  });
                  decorations.push(decoration);
                }
              } catch (error) {
                console.warn('Error creating decoration for change:', change.id, error);
              }
            });

            return decorations.length > 0 ? decorationSet.add(tr.doc, decorations) : decorationSet;
          }
        },
        props: {
          decorations(state) {
            return this.getState(state);
          }
        }
      })
    ];
  }
});

export const EnhancedCollaborativeEditor: React.FC<EnhancedCollaborativeEditorProps> = ({
  content,
  onChange,
  bookId,
  sceneId,
  currentUserId,
  currentUserName,
  userRole,
  editChanges = [],
  onAcceptChange,
  onRejectChange,
  className = '',
  placeholder = 'Start writing...',
  hideToolbar = false,
  readOnly = false
}) => {
  const [isTracking, setIsTracking] = useState(false);
  const [pendingChanges, setPendingChanges] = useState<EditChange[]>([]);
  const [showChangeReview, setShowChangeReview] = useState(false);
  const lastContentRef = useRef(content);
  const changeTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isUpdatingFromProp = useRef(false);



  // Clear history when scene changes by forcing editor recreation
  const editorKey = sceneId || 'default';

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        history: false,
        bulletList: false,
        orderedList: false,
        listItem: false,
        codeBlock: false,
        blockquote: false,
        horizontalRule: false,
      }),
      Underline,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      HorizontalRule,
      History.configure({
        depth: 50,
      }),
      ChangeTrackingExtension,
    ],
    content,
    onUpdate: ({ editor }) => {
      // Prevent infinite loops when updating from props
      if (isUpdatingFromProp.current) {
        return;
      }

      const newContent = editor.getHTML();
      
      // Immediate onChange for responsive typing
      onChange(newContent);
      
      // Track changes for editors with debouncing to prevent keystroke interruption
      if (userRole === 'editor' && !isTracking) {
        handleContentChangeDebounced(newContent);
      }
      
      lastContentRef.current = newContent;
    },
    editorProps: {
      attributes: {
        class: `prose prose-lg max-w-none focus:outline-none text-gray-900 leading-relaxed ${userRole === 'editor' ? 'border-l-4 border-blue-500' : ''}`,
        style: 'font-family: "Crimson Text", serif; font-size: 16pt; line-height: 1.6;',
        placeholder,
      },
    },
    editable: !readOnly,
  }, [editorKey, userRole]);

  // Debounced change tracking to prevent keystroke interruption
  const handleContentChangeDebounced = useCallback((newContent: string) => {
    // Clear existing timeout
    if (changeTimeoutRef.current) {
      clearTimeout(changeTimeoutRef.current);
    }

    // Set new timeout for change tracking (longer delay to prevent interruption)
    changeTimeoutRef.current = setTimeout(async () => {
      if (userRole === 'editor' && lastContentRef.current !== newContent) {
        setIsTracking(true);
        
        try {
          const change = detectChange(lastContentRef.current, newContent);
          if (change) {
            const editChange: EditChange = {
              id: `change_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              userId: currentUserId,
              userName: currentUserName,
              type: change.type,
              position: change.position,
              originalText: change.originalText,
              newText: change.newText,
              timestamp: new Date().toISOString(),
              isAccepted: null
            };

            // Add to pending changes for immediate visual feedback
            setPendingChanges(prev => [...prev, editChange]);

            // Track in Firebase (non-blocking)
            CollaborativeEditingService.trackEditChange(bookId, sceneId, {
              userId: currentUserId,
              userName: currentUserName,
              type: change.type,
              position: change.position,
              originalText: change.originalText,
              newText: change.newText,
              isAccepted: null
            }).then(result => {
              if (!result.success) {
                console.error('Failed to track change:', result.error);
                // Remove from pending changes if Firebase fails
                setPendingChanges(prev => prev.filter(c => c.id !== editChange.id));
              }
            }).catch(error => {
              console.error('Error tracking change:', error);
              // Remove from pending changes if Firebase fails
              setPendingChanges(prev => prev.filter(c => c.id !== editChange.id));
            });

            // Update editor decorations
            if (editor) {
              const tr = editor.state.tr;
              tr.setMeta('editChanges', [...editChanges, editChange]);
              editor.view.dispatch(tr);
            }
          }
        } catch (error) {
          console.error('Error in change tracking:', error);
        } finally {
          setIsTracking(false);
        }
      }
    }, 1500); // Longer delay to prevent keystroke interruption
  }, [bookId, sceneId, currentUserId, currentUserName, userRole, editChanges, editor]);

  // Update editor content when the content prop changes (but not from editor updates)
  useEffect(() => {
    if (editor && content !== editor.getHTML()) {
      isUpdatingFromProp.current = true;
      editor.commands.setContent(content, false);
      lastContentRef.current = content;
      // Reset flag after a short delay
      setTimeout(() => {
        isUpdatingFromProp.current = false;
      }, 100);
    }
  }, [content, editor]);

  // Update pending changes when editChanges prop changes
  useEffect(() => {
    const newPendingChanges = editChanges.filter(change => change.isAccepted === null);
    setPendingChanges(newPendingChanges);
    
    // Update editor decorations
    if (editor) {
      const tr = editor.state.tr;
      tr.setMeta('editChanges', editChanges);
      editor.view.dispatch(tr);
    }
  }, [editChanges, editor]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (changeTimeoutRef.current) {
        clearTimeout(changeTimeoutRef.current);
      }
    };
  }, []);

  // Improved change detection algorithm
  const detectChange = (oldText: string, newText: string): Omit<EditChange, 'id' | 'userId' | 'userName' | 'timestamp' | 'isAccepted'> | null => {
    if (oldText === newText) return null;

    // Convert HTML to plain text for comparison
    const stripHtml = (html: string) => {
      const div = document.createElement('div');
      div.innerHTML = html;
      return div.textContent || div.innerText || '';
    };

    const oldPlain = stripHtml(oldText);
    const newPlain = stripHtml(newText);



    if (oldPlain === newPlain) return null;

    // Find the first position where texts differ
    let startPos = 0;
    while (startPos < Math.min(oldPlain.length, newPlain.length) &&
           oldPlain[startPos] === newPlain[startPos]) {
      startPos++;
    }

    // Find the last position where texts differ (working backwards)
    let oldEndPos = oldPlain.length;
    let newEndPos = newPlain.length;

    while (oldEndPos > startPos && newEndPos > startPos &&
           oldPlain[oldEndPos - 1] === newPlain[newEndPos - 1]) {
      oldEndPos--;
      newEndPos--;
    }

    const originalText = oldPlain.substring(startPos, oldEndPos);
    const newTextContent = newPlain.substring(startPos, newEndPos);



    // Determine change type
    if (originalText.length === 0 && newTextContent.length > 0) {
      return {
        type: 'insert',
        position: startPos,
        originalText: '',
        newText: newTextContent
      };
    } else if (originalText.length > 0 && newTextContent.length === 0) {
      return {
        type: 'delete',
        position: startPos,
        originalText: originalText,
        newText: ''
      };
    } else if (originalText.length > 0 && newTextContent.length > 0) {
      return {
        type: 'replace',
        position: startPos,
        originalText: originalText,
        newText: newTextContent
      };
    }

    return null;
  };



  // Handle accepting/rejecting changes
  const handleAcceptChange = useCallback(async (changeId: string) => {
    if (onAcceptChange) {
      onAcceptChange(changeId);
    }
    
    try {
      await CollaborativeEditingService.reviewEditChange(
        bookId,
        sceneId,
        changeId,
        true,
        currentUserId
      );
      
      // Remove from pending changes
      setPendingChanges(prev => prev.filter(c => c.id !== changeId));
    } catch (error) {
      console.error('Error accepting change:', error);
    }
  }, [bookId, sceneId, currentUserId, onAcceptChange]);

  const handleRejectChange = useCallback(async (changeId: string) => {
    if (onRejectChange) {
      onRejectChange(changeId);
    }
    
    try {
      await CollaborativeEditingService.reviewEditChange(
        bookId,
        sceneId,
        changeId,
        false,
        currentUserId
      );
      
      // Remove from pending changes
      setPendingChanges(prev => prev.filter(c => c.id !== changeId));
    } catch (error) {
      console.error('Error rejecting change:', error);
    }
  }, [bookId, sceneId, currentUserId, onRejectChange]);

  if (!editor) {
    return null;
  }

  const ToolbarButton: React.FC<{
    onClick: () => void;
    isActive?: boolean;
    disabled?: boolean;
    children: React.ReactNode;
    title?: string;
  }> = ({ onClick, isActive = false, disabled = false, children, title }) => (
    <button
      onClick={onClick}
      disabled={disabled}
      title={title}
      className={`p-2 rounded-lg transition-colors ${
        isActive
          ? 'bg-blue-100 text-blue-600'
          : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
      } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
    >
      {children}
    </button>
  );

  const pendingEditChanges = pendingChanges.filter(change => change.isAccepted === null);

  return (
    <div key={editorKey} className={`bg-white ${className.includes('focus-mode') ? '' : 'rounded-2xl shadow-2xl'} ${className} flex flex-col h-full ${readOnly ? 'read-only-editor' : ''} ${userRole === 'editor' ? 'border-l-4 border-blue-500' : ''}`}>
      


      {/* Role Indicator */}
      {userRole && userRole !== 'author' && (
        <div className="px-4 py-2 bg-blue-50 border-b border-blue-200 text-sm">
          <span className="text-blue-800 font-medium">
            Editing as: <span className="capitalize">{userRole}</span>
          </span>
          {userRole === 'editor' && (
            <span className="ml-2 text-blue-600">
              • Your changes are highlighted in blue
              {isTracking && <span className="ml-2 text-orange-600">• Tracking changes...</span>}
              {pendingChanges.length > 0 && <span className="ml-2 text-green-600">• {pendingChanges.length} pending changes</span>}

            </span>
          )}
        </div>
      )}

      {/* Change Review Panel for Authors */}
      {userRole === 'author' && pendingEditChanges.length > 0 && (
        <div className="px-4 py-3 bg-blue-50 border-b border-blue-200">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-blue-800">
              {pendingEditChanges.length} pending change{pendingEditChanges.length !== 1 ? 's' : ''}
            </span>
            <button
              onClick={() => setShowChangeReview(!showChangeReview)}
              className="text-sm text-blue-600 hover:text-blue-700"
            >
              {showChangeReview ? 'Hide' : 'Review'}
            </button>
          </div>
          
          {showChangeReview && (
            <div className="space-y-2">
              {pendingEditChanges.map(change => (
                <div key={change.id} className="flex items-center justify-between p-2 bg-white rounded border">
                  <div className="flex-1">
                    <div className="flex items-center text-sm">
                      <User className="w-3 h-3 mr-1 text-gray-500" />
                      <span className="font-medium">{change.userName}</span>
                      <Clock className="w-3 h-3 ml-2 mr-1 text-gray-500" />
                      <span className="text-gray-500">
                        {new Date(change.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                    <div className="text-sm text-gray-700 mt-1">
                      {change.type === 'insert' && `Added: "${change.newText}"`}
                      {change.type === 'delete' && `Deleted: "${change.originalText}"`}
                      {change.type === 'replace' && `Changed "${change.originalText}" to "${change.newText}"`}
                    </div>
                  </div>
                  <div className="flex space-x-1">
                    <button
                      onClick={() => handleAcceptChange(change.id)}
                      className="p-1 text-green-600 hover:bg-green-100 rounded"
                      title="Accept change"
                    >
                      <Check className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handleRejectChange(change.id)}
                      className="p-1 text-red-600 hover:bg-red-100 rounded"
                      title="Reject change"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Toolbar */}
      {!hideToolbar && !readOnly && (
        <div className="border-b border-gray-200 p-4">
          <div className="flex flex-wrap gap-1">
            {/* Text Formatting */}
            <div className="flex gap-1 mr-4">
              <ToolbarButton
                onClick={() => editor.chain().focus().toggleBold().run()}
                isActive={editor.isActive('bold')}
                title="Bold"
              >
                <Bold className="w-4 h-4" />
              </ToolbarButton>
              <ToolbarButton
                onClick={() => editor.chain().focus().toggleItalic().run()}
                isActive={editor.isActive('italic')}
                title="Italic"
              >
                <Italic className="w-4 h-4" />
              </ToolbarButton>
              <ToolbarButton
                onClick={() => editor.chain().focus().toggleUnderline().run()}
                isActive={editor.isActive('underline')}
                title="Underline"
              >
                <UnderlineIcon className="w-4 h-4" />
              </ToolbarButton>
              <ToolbarButton
                onClick={() => editor.chain().focus().toggleStrike().run()}
                isActive={editor.isActive('strike')}
                title="Strikethrough"
              >
                <Strikethrough className="w-4 h-4" />
              </ToolbarButton>
            </div>

            {/* Alignment */}
            <div className="flex gap-1 mr-4">
              <ToolbarButton
                onClick={() => editor.chain().focus().setTextAlign('left').run()}
                isActive={editor.isActive({ textAlign: 'left' })}
                title="Align Left"
              >
                <AlignLeft className="w-4 h-4" />
              </ToolbarButton>
              <ToolbarButton
                onClick={() => editor.chain().focus().setTextAlign('center').run()}
                isActive={editor.isActive({ textAlign: 'center' })}
                title="Align Center"
              >
                <AlignCenter className="w-4 h-4" />
              </ToolbarButton>
              <ToolbarButton
                onClick={() => editor.chain().focus().setTextAlign('right').run()}
                isActive={editor.isActive({ textAlign: 'right' })}
                title="Align Right"
              >
                <AlignRight className="w-4 h-4" />
              </ToolbarButton>
              <ToolbarButton
                onClick={() => editor.chain().focus().setTextAlign('justify').run()}
                isActive={editor.isActive({ textAlign: 'justify' })}
                title="Justify"
              >
                <AlignJustify className="w-4 h-4" />
              </ToolbarButton>
            </div>

            {/* Divider */}
            <div className="flex gap-1 mr-4">
              <ToolbarButton
                onClick={() => editor.chain().focus().setHorizontalRule().run()}
                title="Add Divider"
              >
                <Minus className="w-4 h-4" />
              </ToolbarButton>
            </div>

            {/* History */}
            <div className="flex gap-1">
              <ToolbarButton
                onClick={() => editor.chain().focus().undo().run()}
                disabled={!editor.can().undo()}
                title="Undo"
              >
                <Undo className="w-4 h-4" />
              </ToolbarButton>
              <ToolbarButton
                onClick={() => editor.chain().focus().redo().run()}
                disabled={!editor.can().redo()}
                title="Redo"
              >
                <Redo className="w-4 h-4" />
              </ToolbarButton>
            </div>
          </div>
        </div>
      )}

      {/* Editor */}
      <div className="p-8 flex-1 overflow-y-auto min-h-0">
        <EditorContent editor={editor} className="h-full" />
      </div>

      {/* CSS for change highlighting */}
      <style>{`
        /* Global styles for edit changes */
        .edit-change {
          position: relative !important;
          cursor: pointer !important;
          padding: 2px 4px !important;
          margin: 0 2px !important;
          border-radius: 3px !important;
          display: inline !important;
        }

        .edit-change.edit-insert {
          background-color: #dbeafe !important;
          border-bottom: 2px solid #3b82f6 !important;
          color: #1e40af !important;
        }

        .edit-change.edit-delete {
          background-color: #fee2e2 !important;
          border-bottom: 2px solid #ef4444 !important;
          text-decoration: line-through !important;
          color: #dc2626 !important;
        }

        .edit-change.edit-replace {
          background-color: #fef3c7 !important;
          border-bottom: 2px solid #f59e0b !important;
          color: #d97706 !important;
        }

        .edit-change:hover::after {
          content: attr(data-user) !important;
          position: absolute !important;
          top: -25px !important;
          left: 0 !important;
          background: rgba(0, 0, 0, 0.8) !important;
          color: white !important;
          padding: 2px 6px !important;
          border-radius: 4px !important;
          font-size: 12px !important;
          white-space: nowrap !important;
          z-index: 1000 !important;
          pointer-events: none !important;
        }

        /* ProseMirror specific styles */
        .ProseMirror .edit-change {
          position: relative !important;
          cursor: pointer !important;
          padding: 2px 4px !important;
          margin: 0 2px !important;
          border-radius: 3px !important;
          display: inline !important;
        }

        .ProseMirror .edit-change.edit-insert {
          background-color: #dbeafe !important;
          border-bottom: 2px solid #3b82f6 !important;
          color: #1e40af !important;
        }

        .ProseMirror .edit-change.edit-delete {
          background-color: #fee2e2 !important;
          border-bottom: 2px solid #ef4444 !important;
          text-decoration: line-through !important;
          color: #dc2626 !important;
        }

        .ProseMirror .edit-change.edit-replace {
          background-color: #fef3c7 !important;
          border-bottom: 2px solid #f59e0b !important;
          color: #d97706 !important;
        }

        /* Ensure visibility over other styles */
        .ProseMirror-focused .edit-change {
          background-color: inherit !important;
        }

        .ProseMirror-focused .edit-change.edit-insert {
          background-color: #dbeafe !important;
        }

        .ProseMirror-focused .edit-change.edit-delete {
          background-color: #fee2e2 !important;
        }

        .ProseMirror-focused .edit-change.edit-replace {
          background-color: #fef3c7 !important;
        }
      `}</style>
    </div>
  );
};